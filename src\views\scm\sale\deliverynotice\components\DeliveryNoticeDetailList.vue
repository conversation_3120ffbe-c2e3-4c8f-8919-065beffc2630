<template>
  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="发货明细ID" align="center" prop="id" />
      <el-table-column label="行号" align="center" prop="num" />
      <el-table-column label="发货通知单单号" align="center" prop="noticeNo" />
      <el-table-column label="销售单单号" align="center" prop="saleOrderNo" />
      <el-table-column label="销售订单明细ID" align="center" prop="saleOrderDetailId" />
      <el-table-column label="物料编号" align="center" prop="materialCode" />
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="物料规格" align="center" prop="materialSpec" />
      <el-table-column label="物料发货数量" align="center" prop="materialQuantity" />
      <el-table-column label="物料单价" align="center" prop="materialUnitPrice" />
      <el-table-column label="物料总金额" align="center" prop="totalAmount" />
      <el-table-column label="基本单位数量" align="center" prop="baseQuantity" />
      <el-table-column label="要求" align="center" prop="packagingRequirements" />
      <el-table-column label="源单类型" align="center" prop="sourceType" />
      <el-table-column label="源单编号" align="center" prop="sourceId" />
      <el-table-column label="源单单号" align="center" prop="sourceNo" />
      <el-table-column label="仓库编号" align="center" prop="warehouseId" />
      <el-table-column
        label="交货日期"
        align="center"
        prop="deliveryDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="金蝶系统订单ID" align="center" prop="kdOrderId" />
      <el-table-column label="金蝶系统ID" align="center" prop="kdId" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="创建者名称" align="center" prop="creatorName" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="更新者名称" align="center" prop="updaterName" />
    </el-table>
  </ContentWrap>
</template>
<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { DeliveryNoticeApi } from '@/api/scm/sale/deliverynotice'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const props = defineProps<{
  noticeId?: number // 发货通知单ID（主表的关联字段）
}>()
const loading = ref(false) // 列表的加载中
const list = ref([]) // 列表的数据

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    list.value = await DeliveryNoticeApi.getDeliveryNoticeDetailListByNoticeId(props.noticeId)
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
