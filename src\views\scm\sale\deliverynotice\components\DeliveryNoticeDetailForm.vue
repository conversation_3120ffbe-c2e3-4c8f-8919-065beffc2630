<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px" border show-summary :summary-method="summaryMethod">
      <!-- <el-table-column label="序号" type="index" width="100" /> -->
      <el-table-column label="行号" min-width="60" align="center" prop="num" />
       <!-- <el-table-column label="发货通知单单号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.noticeNo`" :rules="formRules.noticeNo" class="mb-0px!">
            <el-input v-model="row.noticeNo" placeholder="请输入发货通知单单号" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="销售单ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.saleOrderId`" :rules="formRules.saleOrderId" class="mb-0px!">
            <el-input v-model="row.saleOrderId" placeholder="请输入销售单ID" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="销售单号" min-width="150">
        <template #default="{ row }">
          <span>{{ row.saleOrderNo || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料名称" min-width="240">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialName`" :rules="formRules.materialName" class="mb-0px!">
            <ScrollSelect
              v-if="row.materialId || !row.materialName"
              filterable
              clearable
              class="!w-240px"
              v-model="row.materialId"
              :load-method="getRemoteMaterial"
              :label-key="formatMaterialLabel"
              value-key="id"
              :default-value="{id: row.materialId, name: row.materialName, fullCode: row.materialCode, spec: row.materialSpec}"
              query-key="name"
              :extra-params="{ 'types': ([1,2])}"
              @change="(val, material) => {
                row.materialName = material?.name
                row.materialCode = material?.fullCode
                // 确保单位ID有效，如果saleUnit无效则尝试使用其他单位
                const unitId = material?.saleUnit || material?.unit || material?.purchaseUnit
                row.materialUnit = unitId ? Number(unitId) : undefined
                row.materialSpec = material?.spec
                // 获取批次数据
                loadBatchOptions(row, material?.id)
              }"
            />
            <!-- 当物料ID不存在但有物料名称时，显示为只读文本，点击时切换为选择模式 -->
            <el-input
              v-else-if="!row.materialId && row.materialName"
              v-model="row.materialName"
              placeholder="物料名称"
              readonly
              class="!w-240px cursor-pointer"
              @click="convertToSelectMode(row)"
              title="点击切换为选择模式"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="物料编码" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialCode`" :rules="formRules.materialCode" class="mb-0px!">
            <el-input v-model="row.materialCode" placeholder="物料编码" disabled/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="规格" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialSpec`" class="mb-0px!">
            <el-input v-model="row.materialSpec" placeholder="规格" disabled/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="发货数量" min-width="150" prop="materialQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialQuantity`" :rules="formRules.materialQuantity" class="mb-0px!">
            <el-input v-model="row.materialQuantity" placeholder="请输入物料发货数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="单位" min-width="100" prop="materialUnit">
        <template #default="{ row }">
          <span>{{ getUnitName(row.materialUnit) || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="单价" min-width="150" prop="materialUnitPrice">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialUnitPrice`" :rules="formRules.materialUnitPrice" class="mb-0px!">
            <el-input v-model="row.materialUnitPrice" placeholder="请输入物料单价" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="总金额" min-width="150" prop="totalAmount">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.totalAmount`" :rules="formRules.totalAmount" class="mb-0px!">
            <el-input v-model="row.totalAmount" placeholder="请输入物料总金额" />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="基本单位数量" min-width="150" prop="baseQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.baseQuantity`" :rules="formRules.baseQuantity" class="mb-0px!">
            <el-input v-model="row.baseQuantity" placeholder="请输入基本单位数量" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="仓库" min-width="200">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.warehouseId`" :rules="formRules.warehouseId" class="mb-0px!">
            <el-tree-select
              v-model="row.warehouseId"
              :data="warehouseTreeData"
              placeholder="请选择仓库"
              clearable
              check-strictly
              :render-after-expand="false"
              class="!w-180px"
              node-key="id"
              :props="{
                value: 'id',
                label: 'name',
                children: 'children',
                disabled: 'disabled'
              }"
              @change="handleWarehouseChange($index, $event)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="批号" min-width="200">
        <template #default="{row,$index}">
          <el-form-item :prop="`${$index}.batchNo`" :rules="formRules.batchNo" class="mb-0px!">
            <el-select
              v-model="row.batchNo"
              placeholder="请选择批号"
              clearable
              filterable
              class="!w-180px"
              :disabled="!row.materialId"
            >
              <el-option
                v-for="batch in row.batchOptions || []"
                :key="batch.id"
                :label="batch.label"
                :value="batch.value"
                :disabled="batch.disabled"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="要求" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.packagingRequirements`" :rules="formRules.packagingRequirements" class="mb-0px!">
            <el-input v-model="row.packagingRequirements" placeholder="请输入要求" />
          </el-form-item>
        </template>
      </el-table-column>

      <!-- <el-table-column label="源单编号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.sourceId`" :rules="formRules.sourceId" class="mb-0px!">
            <el-input v-model="row.sourceId" placeholder="请输入源单编号" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="源单单号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.sourceNo`" :rules="formRules.sourceNo" class="mb-0px!">
            <el-input v-model="row.sourceNo" placeholder="请输入源单单号" disabled/>
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="交货日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.deliveryDate`" :rules="formRules.deliveryDate" class="mb-0px!">
            <el-date-picker
              v-model="row.deliveryDate"
              type="date"
              value-format="x"
              placeholder="选择交货日期"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="金蝶系统订单ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.kdOrderId`" :rules="formRules.kdOrderId" class="mb-0px!">
            <el-input v-model="row.kdOrderId" placeholder="请输入金蝶系统订单ID" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="金蝶系统ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.kdId`" :rules="formRules.kdId" class="mb-0px!">
            <el-input v-model="row.kdId" placeholder="请输入金蝶系统ID" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" :rules="formRules.remark" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="创建者名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.creatorName`" :rules="formRules.creatorName" class="mb-0px!">
            <el-input v-model="row.creatorName" placeholder="请输入创建者名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="更新者名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.updaterName`" :rules="formRules.updaterName" class="mb-0px!">
            <el-input v-model="row.updaterName" placeholder="请输入更新者名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="租户名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.tenantName`" :rules="formRules.tenantName" class="mb-0px!">
            <el-input v-model="row.tenantName" placeholder="请输入租户名称" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <Icon icon="ep:delete" @click="handleDelete($index)" color="#f56c6c"/>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加发货通知单明细</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { DeliveryNoticeApi } from '@/api/scm/sale/deliverynotice'

import { WarehouseApi } from '@/api/scm/inventory/warehouse'
import { OrderApi } from '@/api/scm/sale/order'
import { MaterialApi, MaterialVO } from '@/api/scm/base/material'
import { BatchInfoApi } from '@/api/scm/inventory/batchinfo'
import {
  mountBatchOptionsToRows,
  validateBatchData,
  cleanGeneratedBatchNumbers,
  type BatchDataByMaterial
} from '@/utils/batchUtils'
import ScrollSelect from '@/components/ScrollSelect/index.vue'
// import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { formatAmount, formatQuantity } from '@/utils/formatter'
import { handleTree } from '@/utils/tree'

const props = defineProps<{
  noticeId?: number // 发货通知单ID（主表的关联字段）
  orderId?: number // 订单ID（用于从订单创建发货通知单）
  saleOrderNo?: string // 销售订单号（用于预填充销售单单号）
  unitMap?: Map<number, string> // 单位ID到名称的映射
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref<any>([])
const formRules = reactive<any>({
  noticeId: [{ required: true, message: '发货通知单ID不能为空', trigger: 'blur' }],
  materialName: [{ required: true, message: '物料名称不能为空', trigger: 'blur' }],
  materialCode: [{ required: true, message: '物料编码不能为空', trigger: 'blur' }],
  batchNo: [
    { required: false, message: '批号不能为空', trigger: 'blur' },
    { max: 50, message: '批号长度不能超过50个字符', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9\-_]+$/, message: '批号只能包含字母、数字、横线和下划线', trigger: 'blur' }
  ],
})
const formRef = ref() // 表单 Ref

// 仓库相关数据
const warehouseList = ref<any[]>([]) // 仓库列表
const warehouseTreeData = ref<any[]>([]) // 仓库树形数据
const warehouseMap = ref(new Map()) // 仓库ID到名称的映射

// 单位数据现在从父组件传递过来

/** 从订单加载明细数据 */
const loadOrderDetails = async (orderId: number) => {
  try {
    formLoading.value = true
    // 获取订单明细数据
    const response = await OrderApi.getOrderDetailListByOrderId({}, orderId)
    const orderDetails = response.list || []

    // 将订单明细转换为发货通知明细格式
    const deliveryNoticeDetails = orderDetails.map((detail: any, index: number) => {
      // 根据物料编码设置默认仓库
      let defaultWarehouseId: number | undefined = undefined
      const materialCode = detail.productCode || ''
      if (materialCode) {
        if (materialCode.startsWith('1')) {
          // 物料编码以1开头，设置为原材料仓
          defaultWarehouseId = 6256
        } else if (materialCode.startsWith('2')) {
          // 物料编码以2开头，设置为产成品仓
          defaultWarehouseId = 6258
        }
      }

      return {
        id: undefined,
        num: index + 1,
        noticeId: props.noticeId,
        noticeNo: undefined,
        saleOrderId: orderId,
        saleOrderNo: props.saleOrderNo,
        saleOrderDetailId: detail.id, // 销售订单明细ID
        materialId: detail.productId,
        materialCode: detail.productCode,
        materialName: detail.productName,
        materialSpec: detail.spec,
        materialUnit: detail.unit ? Number(detail.unit) : undefined, // 确保单位ID为数字类型
        materialQuantity: detail.quantity,
        materialUnitPrice: detail.unitPrice,
        totalAmount: detail.total,
        packagingRequirements: undefined,
        sourceType: 'sale_order', // 销售订单来源，需要与字典值匹配
        sourceId: orderId,
        sourceNo: props.saleOrderNo,
        sourceDetailId: detail.id,
        warehouseId: defaultWarehouseId,
        batchNo: undefined,
        batchOptions: [], // 添加批次选项
        deliveryDate: detail.deliveryDate,
        kdOrderId: undefined,
        kdId: undefined,
        remark: detail.remark,
        creatorName: undefined,
        updaterName: undefined,
        tenantName: undefined,
      }
    })

    // 设置表单数据
    formData.value = deliveryNoticeDetails

    // 为每行添加批次选项字段
    deliveryNoticeDetails.forEach(row => {
      if (!row.batchOptions) {
        row.batchOptions = []
      }
    })

    // 批量加载所有物料的批次信息
    await loadBatchOptionsForMultipleMaterials(deliveryNoticeDetails)
  } catch (error) {
    console.error('加载订单明细失败:', error)
  } finally {
    formLoading.value = false
  }
}

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.noticeId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      const details = await DeliveryNoticeApi.getDeliveryNoticeDetailListByNoticeId(val)
      
      // 为每行添加批次选项字段
      details.forEach(row => {
        row.batchOptions = []
      })

      // 批量加载所有物料的批次信息
      await loadBatchOptionsForMultipleMaterials(details)
      
      formData.value = details
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 监听订单ID变化，从订单加载明细数据 */
watch(
  () => props.orderId,
  async (orderId) => {
    // 只有在新建发货通知单时（没有noticeId）且有orderId时才加载订单明细
    if (!props.noticeId && orderId) {
      await loadOrderDetails(orderId)
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row:any = {
    id: undefined,
    num: formData.value.length + 1,
    noticeId: props.noticeId,
    noticeNo: undefined,
    saleOrderId: props.orderId,
    saleOrderNo: props.saleOrderNo,
    saleOrderDetailId: undefined, // 销售订单明细ID
    materialId: undefined,
    materialCode: undefined,
    materialName: undefined,
    materialSpec: undefined,
    materialUnit: undefined, // 添加单位字段
    materialQuantity: undefined,
    materialUnitPrice: undefined,
    totalAmount: undefined,
    packagingRequirements: undefined,
    // 移除源单信息的自动填充
    sourceType: undefined,
    sourceId: undefined,
    sourceNo: undefined,
    warehouseId: undefined,
    batchNo: undefined,
    batchOptions: [], // 添加批次选项
    deliveryDate: undefined,
    kdOrderId: undefined,
    kdId: undefined,
    remark: undefined,
    creatorName: undefined,
    updaterName: undefined,
    tenantName: undefined,
  }
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  // 过滤掉batchOptions字段，避免传递给后端
  const cleanData = formData.value.map(item => {
    const { batchOptions, ...rest } = item
    return rest
  })

  // 清理生成的批号
  return cleanGeneratedBatchNumbers(cleanData)
}

/** 物料远程加载方法 */
const getRemoteMaterial = async (params) => {
  const { pageNo, pageSize, query, ...restParams } = params;
  const response = await MaterialApi.getSimpleMaterialPage({
    pageNo,
    pageSize,
    name: query, // 映射到 API 的 `name` 字段
    ...restParams,
  });
  const { list, total } = await response;
  return { list, total };
}

/** 格式化物料选项显示标签 */
const formatMaterialLabel = (material: MaterialVO) => {
  if (!material) return ''
  const fullCode = material.fullCode || ''
  const name = material.name || ''
  const spec = material.spec || ''

  // 构建显示标签，只有非空值才参与拼接
  const parts: string[] = []
  if (fullCode) parts.push(fullCode)
  if (name) parts.push(name)
  if (spec) parts.push(spec)

  return parts.join(' - ')
}

/** 转换为选择模式 */
const convertToSelectMode = (row: any) => {
  // 清空物料相关字段，让用户重新选择
  row.materialId = undefined
  row.materialName = undefined
  row.materialCode = undefined
  row.materialSpec = undefined
  row.materialUnit = undefined
  row.batchNo = undefined
  row.batchOptions = []
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = ['materialQuantity']

    // 需要汇总的金额字段
    const amountFields = ['materialUnitPrice', 'totalAmount']

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatQuantity(total)
    } else if (amountFields.includes(column.property)) {
      // 金额字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatAmount(total)
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

/** 加载仓库数据 */
const loadWarehouses = async () => {
  try {
    const data = await WarehouseApi.getWarehouseList({
      pageNo: 1,
      pageSize: 100
    })
    warehouseList.value = data || []

    // 构建仓库ID到名称的映射
    warehouseMap.value.clear()
    data?.forEach((warehouse: any) => {
      warehouseMap.value.set(warehouse.id, warehouse.name)
    })

    // 构建树形数据
    const treeData = handleTree(data || [], 'id', 'parentId')

    // 为树形数据添加禁用标识，只有叶子节点可以选择
    const markLeafNodes = (nodes: any[]): any[] => {
      return nodes.map(node => {
        const hasChildren = node.children && node.children.length > 0
        return {
          ...node,
          disabled: hasChildren, // 有子节点的节点禁用
          children: hasChildren ? markLeafNodes(node.children) : undefined
        }
      })
    }

    warehouseTreeData.value = markLeafNodes(treeData)
  } catch (error) {
    console.error('获取仓库列表失败:', error)
    warehouseList.value = []
    warehouseTreeData.value = []
  }
}

/** 获取单位名称 */
const getUnitName = (unitId: number | string) => {
  if (!unitId && unitId !== 0) return ''
  
  // 确保unitId是数字类型
  const id = typeof unitId === 'string' ? parseInt(unitId) : unitId
  
  if (isNaN(id)) {
    console.warn('无效的单位ID:', unitId)
    return ''
  }
  
  const unitName = props.unitMap?.get(id)
  if (!unitName) {
    console.warn('未找到单位ID对应的名称:', id, '可用单位:', Array.from(props.unitMap?.entries() || []))
    return ''
  }
  
  return unitName
}

/** 仓库选择变化处理 */
const handleWarehouseChange = (index: number, warehouseId: number) => {
  if (formData.value[index]) {
    formData.value[index].warehouseId = warehouseId
  }
}

/** 批量加载批次选项（多个物料） */
const loadBatchOptionsForMultipleMaterials = async (rows: any[]) => {
  // 收集所有需要加载批次的物料ID
  const materialIds = rows
    .filter(row => row.materialId)
    .map(row => Number(row.materialId))
    .filter((id, index, arr) => arr.indexOf(id) === index) // 去重

  if (materialIds.length === 0) {
    // 如果没有物料ID，清空所有行的批次选项
    mountBatchOptionsToRows(rows, {}, false)
    return
  }

  try {
    // 使用批量接口获取批次数据
    const response = await BatchInfoApi.getSimpleBatchInfoListByMaterialIds({
      materialIds: materialIds
    })

    // 验证批次数据
    const validation = validateBatchData(response as BatchDataByMaterial)
    if (!validation.isValid) {
      validation.errors.forEach(error => console.error(error))
    }
    if (validation.warnings.length > 0) {
      validation.warnings.forEach(warning => console.warn(warning))
    }

    // 使用工具类挂载批次选项到行数据
    mountBatchOptionsToRows(rows, response as BatchDataByMaterial, true)
  } catch (error) {
    console.error('批量加载批次信息失败:', error)
    // 出错时使用工具类清空所有行的批次选项
    mountBatchOptionsToRows(rows, {}, false)
  }
}

/** 加载批次选项（单个物料，向后兼容） */
const loadBatchOptions = async (row: any, materialId: number) => {
  if (!materialId) {
    row.batchOptions = []
    row.batchNo = undefined
    return
  }

  // 使用批量接口处理单个物料
  await loadBatchOptionsForMultipleMaterials([row])
}

// formatBatchLabel 函数已移至 @/utils/batchUtils 工具类中

// 组件挂载时加载仓库数据和批次数据
onMounted(async () => {
  loadWarehouses()
  
  // 如果初始化时已有物料数据，加载对应的批次数据
  if (formData.value && formData.value.length > 0) {
    // 为每行添加批次选项字段
    formData.value.forEach(row => {
      if (!row.batchOptions) {
        row.batchOptions = []
      }
    })

    // 批量加载所有物料的批次信息
    await loadBatchOptionsForMultipleMaterials(formData.value)
  }
})

defineExpose({ validate, getData })
</script>

<style scoped>
/* 表格样式优化 */
</style>
