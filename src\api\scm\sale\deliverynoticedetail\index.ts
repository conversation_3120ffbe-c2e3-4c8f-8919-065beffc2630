import request from '@/config/axios'

// 发货通知单明细 VO
export interface DeliveryNoticeDetailVO {
  id: number // 发货明细ID
  num: number // 行号
  noticeId: number // 发货通知单ID
  noticeNo: string // 发货通知单单号
  saleOrderId: number // 销售单ID
  saleOrderNo: string // 销售单单号
  saleOrderDetailId: number // 销售订单明细ID
  materialId: number // 物料ID
  materialCode: string // 物料编号
  materialName: string // 物料名称
  materialSpec: string // 物料规格
  materialQuantity: number // 物料发货数量
  materialUnitPrice: number // 物料单价
  totalAmount: number // 物料总金额
  materialUnit:number //物料单位
  // baseQuantity: number // 基本单位数量
  packagingRequirements: string // 要求
  sourceType: string // 源单类型
  sourceId: number // 源单编号
  sourceNo: string // 源单单号
  sourceDetailId:number //源单明细ID
  warehouseId: number // 仓库编号
  deliveryDate: Date // 交货日期
  kdOrderId: number // 金蝶系统订单ID
  batchNo:string //批号
  kdId: number // 金蝶系统ID
  remark: string // 备注
  creatorName: string // 创建者名称
  updaterName: string // 更新者名称
  tenantName: string // 租户名称
}

// 发货通知单明细 API
export const DeliveryNoticeDetailApi = {
  // 查询发货通知单明细分页
  getDeliveryNoticeDetailPage: async (params: any) => {
    return await request.get({ url: `/scm/sale/delivery-notice-detail/page`, params })
  },

  // 查询发货通知单明细详情
  getDeliveryNoticeDetail: async (id: number) => {
    return await request.get({ url: `/scm/sale/delivery-notice-detail/get?id=` + id })
  },

  // 新增发货通知单明细
  createDeliveryNoticeDetail: async (data: DeliveryNoticeDetailVO) => {
    return await request.post({ url: `/scm/sale/delivery-notice-detail/create`, data })
  },

  // 修改发货通知单明细
  updateDeliveryNoticeDetail: async (data: DeliveryNoticeDetailVO) => {
    return await request.put({ url: `/scm/sale/delivery-notice-detail/update`, data })
  },

  // 删除发货通知单明细
  deleteDeliveryNoticeDetail: async (id: number) => {
    return await request.delete({ url: `/scm/sale/delivery-notice-detail/delete?id=` + id })
  },

  // 导出发货通知单明细 Excel
  exportDeliveryNoticeDetail: async (params) => {
    return await request.download({ url: `/scm/sale/delivery-notice-detail/export-excel`, params })
  },
}
