<template>
  <div class="statistics-container">
    <div class="statistics-card-wrapper">
      <el-card shadow="hover" class="statistics-card border-blue">
        <el-row justify="space-between" align="top">
          <el-col :span="16">
            <el-text size="small" class="!text-gray-500">总订单数</el-text>
            <el-text size="large" tag="h3" class="!text-2xl !font-bold !mt-1 !block">
              {{ statistics.saleOrders }}
            </el-text>
            <el-text size="small" class="!text-green-500 !mt-2 !flex !items-center">
              <Icon icon="ep:arrow-up" class="mr-1" /> 较上月增长 {{ statistics.saleGrowth }}%
            </el-text>
          </el-col>
          <el-col :span="8" class="!text-right">
            <el-avatar :size="40" class="!bg-blue-100">
              <Icon icon="ep:shopping-bag" class="!text-blue-500" />
            </el-avatar>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <div class="statistics-card-wrapper">
      <el-card shadow="hover" class="statistics-card border-green">
        <el-row justify="space-between" align="top">
          <el-col :span="16">
            <el-text size="small" class="!text-gray-500">已完成订单</el-text>
            <el-text size="large" tag="h3" class="!text-2xl !font-bold !mt-1 !block">
              {{ statistics.purchaseOrders }}
            </el-text>
            <el-text size="small" class="!text-orange-500 !mt-2 !flex !items-center">
              <Icon icon="ep:arrow-up" class="mr-1" /> 较上月增长 {{ statistics.purchaseGrowth }}%
            </el-text>
          </el-col>
          <el-col :span="8" class="!text-right">
            <el-avatar :size="40" class="!bg-green-100">
              <Icon icon="ep:circle-check" class="!text-green-500" />
            </el-avatar>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <div class="statistics-card-wrapper">
      <el-card shadow="hover" class="statistics-card border-orange">
        <el-row justify="space-between" align="top">
          <el-col :span="16">
            <el-text size="small" class="!text-gray-500">进行中订单</el-text>
            <el-text size="large" tag="h3" class="!text-2xl !font-bold !mt-1 !block">
              {{ statistics.workOrders }}
            </el-text>
            <el-text size="small" class="!text-green-500 !mt-2 !flex !items-center">
              <Icon icon="ep:arrow-up" class="mr-1" /> 较上月增长 {{ statistics.workGrowth }}%
            </el-text>
          </el-col>
          <el-col :span="8" class="!text-right">
            <el-avatar :size="40" class="!bg-yellow-100">
              <Icon icon="ep:clock" class="!text-orange-500" />
            </el-avatar>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <div class="statistics-card-wrapper">
      <el-card shadow="hover" class="statistics-card border-red">
        <el-row justify="space-between" align="top">
          <el-col :span="16">
            <el-text size="small" class="!text-gray-500">逾期订单</el-text>
            <el-text size="large" tag="h3" class="!text-2xl !font-bold !mt-1 !block">
              {{ statistics.completedOrders }}
            </el-text>
            <el-text size="small" class="!text-green-500 !mt-2 !flex !items-center">
              <Icon icon="ep:arrow-up" class="mr-1" /> 较上月增长 {{ statistics.completedGrowth }}%
            </el-text>
          </el-col>
          <el-col :span="8" class="!text-right">
            <el-avatar :size="40" class="!bg-red-100">
              <Icon icon="ep:warning-filled" class="!text-red-500" />
            </el-avatar>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@/components/Icon'
import type { OrderStatistics } from '../types'

interface Props {
  statistics: OrderStatistics
}

defineProps<Props>()
</script>

<style scoped>
/* 统计容器 - 使用CSS Grid确保完美布局 */
.statistics-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  width: 100%;
  margin-bottom: 16px;
}

/* 统计卡片包装器 */
.statistics-card-wrapper {
  display: flex;
  width: 100%;
}

/* 统计卡片样式 */
.statistics-card {
  transition: all 0.3s ease;
  height: 120px;
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  position: relative;
  overflow: hidden;
}

/* 左侧彩色边框 */
.statistics-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
}

.border-blue::before {
  background-color: #3b82f6;
}
.border-green::before {
  background-color: #10b981;
}
.border-orange::before {
  background-color: #f97316;
}
.border-red::before {
  background-color: #ef4444;
}

.statistics-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 16px 20px;
  height: 100%;
}

.statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* 确保卡片内容布局一致 */
.statistics-card .el-row {
  width: 100%;
  height: 100%;
  align-items: center;
}

/* 数字显示样式优化 */
.statistics-card .el-text[tag="h3"] {
  font-size: 1.5rem !important;
  line-height: 1.2 !important;
  font-weight: 700 !important;
  margin: 4px 0 !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 标题文本样式 */
.statistics-card .el-text:first-child {
  font-size: 0.875rem !important;
  line-height: 1.2 !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 增长率文本样式 */
.statistics-card .el-text:last-child {
  font-size: 0.75rem !important;
  line-height: 1.2 !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 头像样式 */
.statistics-card .el-avatar {
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .statistics-container {
    grid-template-columns: repeat(5, 1fr);
  }
}

@media (max-width: 1200px) {
  .statistics-container {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .statistics-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .statistics-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .statistics-card {
    height: 100px;
  }

  .statistics-card :deep(.el-card__body) {
    padding: 12px 16px;
  }

  .statistics-card .el-text[tag="h3"] {
    font-size: 1.25rem !important;
  }
}

@media (max-width: 576px) {
  .statistics-container {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .statistics-card {
    height: 90px;
  }

  .statistics-card :deep(.el-card__body) {
    padding: 10px 16px;
  }
}

/* 确保在任何情况下都占满容器宽度 */
.statistics-container {
  min-width: 100%;
  box-sizing: border-box;
}

/* 防止内容溢出 */
.statistics-card .el-col {
  min-width: 0;
  overflow: hidden;
}

/* 优化文本显示 */
.statistics-card .el-text {
  display: block;
  width: 100%;
  box-sizing: border-box;
}
</style>
