import request from "../../../utils/request";

/**
 * SCM每日订单统计数据API
 * 参考PC端的DailyOrderApi实现
 */

// 查询每日订单数据分页
export function getDailyOrderPageApi(params) {
  return request({
    url: '/scm/stat/daily-order/page',
    method: 'GET',
    params
  })
}

// 查询每日订单数据详情
export function getDailyOrderApi(id) {
  return request({
    url: '/scm/stat/daily-order/get?id=' + id,
    method: 'GET'
  })
}

// 新增每日订单数据
export function createDailyOrderApi(data) {
  return request({
    url: '/scm/stat/daily-order/create',
    method: 'POST',
    data
  })
}

// 修改每日订单数据
export function updateDailyOrderApi(data) {
  return request({
    url: '/scm/stat/daily-order/update',
    method: 'PUT',
    data
  })
}

// 删除每日订单数据
export function deleteDailyOrderApi(id) {
  return request({
    url: '/scm/stat/daily-order/delete?id=' + id,
    method: 'DELETE'
  })
}

// 导出每日订单数据Excel
export function exportDailyOrderApi(params) {
  return request({
    url: '/scm/stat/daily-order/export-excel',
    method: 'GET',
    params
  })
}


