<template>
  <div class="p-6 bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto space-y-8">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center py-20">
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p class="text-gray-600">正在加载数据...</p>
        </div>
      </div>
      
      <!-- 数据内容 -->
      <div v-else>
        <!-- 顶部统计卡片 -->
        <div class="mb-8">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <ScmMetricCard
          title="销售订单"
          tag="今日"
          amount-prefix="¥"
          :decimals="0"
          :amount="todayData.saleAmount || 0"
          :count="todayData.saleCount || 0"
          amount-label="销售金额"
          count-label="销售订单数"
          gradient-class="bg-gradient-to-r from-indigo-500 to-purple-500"
        />
        <ScmMetricCard
          title="出库订单"
          tag="今日"
          amount-prefix="¥"
          :decimals="0"
          :amount="todayData.outStorageAmount || 0"
          :count="todayData.outStorageCount || 0"
          amount-label="出库金额"
          count-label="出库订单数"
          gradient-class="bg-gradient-to-r from-blue-500 to-blue-400"
        />
        <ScmMetricCard
          title="采购订单"
          tag="今日"
          amount-prefix="¥"
          :decimals="0"
          :amount="todayData.purchaseAmout || 0"
          :count="todayData.purchaseCount || 0"
          amount-label="采购金额"
          count-label="采购订单数"
          gradient-class="bg-gradient-to-r from-sky-400 to-cyan-400"
        />
        <ScmMetricCard
          title="生产订单"
          tag="今日"
          amount-prefix="¥"
          :decimals="0"
          :amount="todayData.mfgAmount || 0"
          :count="todayData.mfgCount || 0"
          amount-label="生产金额"
          count-label="生产订单数"
          gradient-class="bg-gradient-to-r from-emerald-400 to-green-400"
        />
          </div>
        </div>

        <!-- 快捷入口和待处理订单区域 -->
        <div class="mb-8">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 待处理订单数据 -->
            <PendingOrderCard :pending-data="pendingData" />
            <!-- 快捷入口 -->
            <QuickAccessCard />
          </div>
        </div>

        <!-- 销售和出库数据折线图 -->
        <div class="mb-8">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SalesDataChart
          :chart-data="chartData"
          @refresh="handleRefreshChart"
        />
        <OutboundDataChart
          :chart-data="chartData"
          @refresh="handleRefreshChart"
        />
          </div>
        </div>

        <!-- 客户地域分布图 -->
        <div class="mb-6">
          <CustomerDistributionChart
            :region-data="regionData"
            @refresh="handleRefreshRegion"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { DailyOrderApi, DailyOrderVO } from '@/api/scm/stat/dailyorder'
import ScmMetricCard from './components/ScmMetricCard.vue'
import QuickAccessCard from './components/QuickAccessCard.vue'
import PendingOrderCard from './components/PendingOrderCard.vue'
import SalesDataChart from './components/DailyDataChart.vue'
import OutboundDataChart from './components/OutboundDataChart.vue'
import CustomerDistributionChart from './components/CustomerDistributionChart.vue'

/** SCM首页 */
defineOptions({ name: 'ScmHome' })

const loading = ref(true)
const todayData = ref<DailyOrderVO>({} as DailyOrderVO)
const chartData = ref<DailyOrderVO[]>([])
const regionData = ref<Array<{ name: string; value: number }>>([])

// 待处理订单数据
const pendingData = ref({
  pendingMfgCount: 0,        // 待生产订单数
  pendingMfgQuantity: 0,     // 待生产数量
  pendingInCount: 0,         // 待入库订单数量
  pendingInQuantity: 0,      // 待入库数量
  pendingAmout: 0,           // 待入库金额
  pendingOutCount: 0,        // 待出库订单数
  pendingOutQuantity: 0,     // 待出库数量
  pendingOutAmout: 0         // 待出库金额
})

// 获取待处理数据
const getPendingData = async () => {
  try {
    // 从今日数据中提取待处理信息
    if (todayData.value.id) {
      pendingData.value = {
        pendingMfgCount: todayData.value.pendingMfgCount || 0,
        pendingMfgQuantity: todayData.value.pendingMfgQuantity || 0,
        pendingInCount: todayData.value.pendingInCount || 0,
        pendingInQuantity: todayData.value.pendingInQuantity || 0,
        pendingAmout: todayData.value.pendingAmout || 0,
        pendingOutCount: todayData.value.pendingOutCount || 0,
        pendingOutQuantity: todayData.value.pendingOutQuantity || 0,
        pendingOutAmout: todayData.value.pendingOutAmout || 0
      }
    }
  } catch (error) {
    console.error('获取待处理数据失败:', error)
  }
}

// 检查API是否可用
const checkApiAvailable = async () => {
  try {
    const testParams = {
      pageNo: 1,
      pageSize: 1
    }
    const testResult = await DailyOrderApi.getDailyOrderPage(testParams)
    
    if (testResult) {
      return true
    } else {
      return false
    }
  } catch (error) {
    console.error('API不可用:', error)
    return false
  }
}

// 获取今日数据
const getTodayData = async () => {
  try {
    const today = new Date().toISOString().split('T')[0]
    
    const params = {
      pageNo: 1,
      pageSize: 1,
      date: today
    }
    
    const result = await DailyOrderApi.getDailyOrderPage(params)
    
    if (result.list && result.list.length > 0) {
      todayData.value = result.list[0]
    } else {
      // 如果今日没有数据，尝试获取最近的数据
      const recentParams = {
        pageNo: 1,
        pageSize: 1
      }
      const recentResult = await DailyOrderApi.getDailyOrderPage(recentParams)
      
      if (recentResult.list && recentResult.list.length > 0) {
        todayData.value = recentResult.list[0]
      }
    }
  } catch (error) {
    console.error('获取今日数据失败:', error)
  }
}



// 获取图表数据
const getChartData = async (days: string = '7') => {
  try {
    const params = {
      pageNo: 1,
      pageSize: parseInt(days)
    }
    
    const result = await DailyOrderApi.getDailyOrderPage(params)
    
    if (result.list) {
      chartData.value = result.list.reverse() // 按日期正序排列
    }
  } catch (error) {
    console.error('获取图表数据失败:', error)
  }
}

// 获取地域分布数据（模拟数据，实际应该从后端获取）
const getRegionData = async (type: string = 'count') => {
  // 模拟数据，实际应该调用相应的API
  // 注意：这里的省份名称需要与地图数据中的名称完全匹配
  regionData.value = [
    { name: '广东省', value: type === 'count' ? 186 : 3280000 },
    { name: '江苏省', value: type === 'count' ? 162 : 2840000 },
    { name: '山东省', value: type === 'count' ? 148 : 2580000 },
    { name: '浙江省', value: type === 'count' ? 135 : 2380000 },
    { name: '河南省', value: type === 'count' ? 118 : 1960000 },
    { name: '四川省', value: type === 'count' ? 109 : 1820000 },
    { name: '湖北省', value: type === 'count' ? 96 : 1610000 },
    { name: '湖南省', value: type === 'count' ? 88 : 1480000 },
    { name: '河北省', value: type === 'count' ? 78 : 1320000 },
    { name: '福建省', value: type === 'count' ? 72 : 1230000 },
    { name: '安徽省', value: type === 'count' ? 66 : 1130000 },
    { name: '江西省', value: type === 'count' ? 58 : 980000 },
    { name: '陕西省', value: type === 'count' ? 52 : 880000 },
    { name: '辽宁省', value: type === 'count' ? 48 : 810000 },
    { name: '重庆市', value: type === 'count' ? 44 : 750000 },
    { name: '上海市', value: type === 'count' ? 92 : 1580000 },
    { name: '北京市', value: type === 'count' ? 85 : 1450000 },
    { name: '天津市', value: type === 'count' ? 38 : 650000 }
  ]

  console.log('地域数据:', regionData.value)
}

// 刷新图表数据
const handleRefreshChart = (days: string) => {
  getChartData(days)
}

// 刷新地域数据
const handleRefreshRegion = (type: string) => {
  getRegionData(type)
}

/** 初始化 */
onMounted(async () => {
  loading.value = true
  
  try {
    // 先检查API是否可用
    const apiAvailable = await checkApiAvailable()
    
    if (apiAvailable) {
      // 先获取今日数据，再获取其他数据
      await getTodayData()
      await Promise.all([
        getChartData(),
        getRegionData(),
        getPendingData()
      ])
    } else {
      await Promise.all([
        getRegionData(), // 地域数据是模拟数据，仍可使用
        getPendingData() // 待处理数据是模拟数据，仍可使用
      ])
    }
  } catch (error) {
    console.error('数据初始化失败:', error)
  } finally {
    loading.value = false
  }
})
</script>
<style lang="scss" scoped>
.row {
  .el-col {
    margin-bottom: 1rem;
  }
}
</style>
